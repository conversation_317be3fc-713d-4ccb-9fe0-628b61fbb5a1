import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))  # Add project root to path
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

# Assuming your project structure allows these imports when pytest runs
# from the project root (e.g., python -m pytest activitylogger/backend/...)
from activitylogger.backend.models import Base, init_default_settings, ActivityLog, Settings, Category, ReportCache
from activitylogger.backend.main import app
from activitylogger.backend.api import get_db as actual_get_db_dependency # The dependency to override

SQLALCHEMY_DATABASE_URL_TEST = "sqlite:///:memory:"  # In-memory SQLite for tests

test_engine = create_engine(
    SQLALCHEMY_DATABASE_URL_TEST,
    connect_args={"check_same_thread": False}  # Required for SQLite with multi-threading/async
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)

@pytest.fixture(scope="function")
def client():
    """
    Provides a TestClient instance that uses an overridden, in-memory test database.
    Ensures a clean slate for each test function by dropping and recreating tables,
    and initializing default settings.
    """
    # 1. Setup: Create tables and initialize default settings
    Base.metadata.drop_all(bind=test_engine)
    Base.metadata.create_all(bind=test_engine)

    db_session_for_init = TestingSessionLocal()
    try:
        # Ensure default settings exist in the test database
        existing_settings = db_session_for_init.query(Settings).first()
        if not existing_settings:
            default_settings = Settings()
            db_session_for_init.add(default_settings)
            db_session_for_init.commit()
            db_session_for_init.refresh(default_settings)
    except Exception as e:
        db_session_for_init.rollback()
        raise e
    finally:
        db_session_for_init.close()

    # 2. Override the get_db dependency for the FastAPI app
    def override_get_db():
        db = TestingSessionLocal()
        try:
            yield db
        finally:
            db.close()

    app.dependency_overrides[actual_get_db_dependency] = override_get_db

    # 3. Yield the TestClient
    with TestClient(app) as c:
        yield c

    # 4. Teardown: Clean up the database and restore original dependency
    Base.metadata.drop_all(bind=test_engine)
    # Remove the override to ensure subsequent tests or app runs are clean
    if actual_get_db_dependency in app.dependency_overrides:
        del app.dependency_overrides[actual_get_db_dependency]