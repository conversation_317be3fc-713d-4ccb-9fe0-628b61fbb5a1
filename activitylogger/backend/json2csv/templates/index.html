<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Media Converter</title>
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --bg-color: #f8fafc;
            --text-color: #1e293b;
            --border-color: #e2e8f0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }

        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.5;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        }

        h1 {
            text-align: center;
            margin-bottom: 2rem;
            color: var(--primary-color);
        }

        .upload-container {
            border: 2px dashed var(--border-color);
            border-radius: 0.5rem;
            padding: 2rem;
            text-align: center;
            margin-bottom: 1.5rem;
            transition: border-color 0.3s;
        }

        .upload-container.dragover {
            border-color: var(--primary-color);
            background-color: #f0f9ff;
        }

        .format-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            font-size: 1rem;
        }

        .convert-btn {
            width: 100%;
            padding: 0.75rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 0.5rem;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .convert-btn:hover {
            background-color: var(--primary-hover);
        }

        .convert-btn:disabled {
            background-color: var(--border-color);
            cursor: not-allowed;
        }

        .progress-container {
            display: none;
            margin-top: 1rem;
        }

        .progress-bar {
            width: 100%;
            height: 0.5rem;
            background-color: var(--border-color);
            border-radius: 0.25rem;
            overflow: hidden;
        }

        .progress {
            width: 0%;
            height: 100%;
            background-color: var(--primary-color);
            transition: width 0.3s;
        }

        .status {
            text-align: center;
            margin-top: 1rem;
            color: var(--text-color);
        }

        .error {
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Media Converter</h1>
        <div class="upload-container" id="dropZone">
            <p>Drag and drop files here or</p>
            <input type="file" id="fileInput" style="display: none">
            <button class="convert-btn" onclick="document.getElementById('fileInput').click()" style="margin-top: 1rem;">
                Choose File
            </button>
        </div>
        
        <select class="format-select" id="formatSelect" disabled>
            <option value="">Select output format...</option>
        </select>

        <button class="convert-btn" id="convertBtn" disabled>
            Convert
        </button>

        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress" id="progress"></div>
            </div>
            <p class="status" id="status">Converting...</p>
        </div>
    </div>

    <script>
        let currentFile = null;
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const formatSelect = document.getElementById('formatSelect');
        const convertBtn = document.getElementById('convertBtn');
        const progressContainer = document.getElementById('progressContainer');
        const progress = document.getElementById('progress');
        const status = document.getElementById('status');

        // Fetch available formats
        fetch('/formats')
            .then(response => response.json())
            .then(formats => {
                const imageFormats = formats.image.map(f => `<option value="${f}">.${f}</option>`).join('');
                const videoFormats = formats.video.map(f => `<option value="${f}">.${f}</option>`).join('');
                formatSelect.innerHTML = `
                    <option value="">Select output format...</option>
                    <optgroup label="Image Formats">
                        ${imageFormats}
                    </optgroup>
                    <optgroup label="Video Formats">
                        ${videoFormats}
                    </optgroup>
                `;
            });

        // File drag and drop
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        dropZone.addEventListener('drop', handleDrop, false);
        fileInput.addEventListener('change', handleFileSelect);
        formatSelect.addEventListener('change', updateConvertButton);

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function highlight(e) {
            dropZone.classList.add('dragover');
        }

        function unhighlight(e) {
            dropZone.classList.remove('dragover');
        }

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const file = dt.files[0];
            handleFile(file);
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            handleFile(file);
        }

        function handleFile(file) {
            currentFile = file;
            dropZone.querySelector('p').textContent = `Selected: ${file.name}`;
            formatSelect.disabled = false;
            updateConvertButton();
        }

        function updateConvertButton() {
            convertBtn.disabled = !(currentFile && formatSelect.value);
        }

        convertBtn.addEventListener('click', async () => {
            if (!currentFile || !formatSelect.value) return;

            const formData = new FormData();
            formData.append('file', currentFile);

            progressContainer.style.display = 'block';
            progress.style.width = '0%';
            status.textContent = 'Converting...';
            status.classList.remove('error');
            convertBtn.disabled = true;

            try {
                const response = await fetch(`/convert?output_format=${formatSelect.value}`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Conversion failed');
                }

                progress.style.width = '100%';
                status.textContent = 'Conversion successful!';

                // Trigger download
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `converted.${formatSelect.value}`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

            } catch (error) {
                progress.style.width = '0%';
                status.textContent = error.message;
                status.classList.add('error');
            } finally {
                convertBtn.disabled = false;
            }
        });
    </script>
</body>
</html>
