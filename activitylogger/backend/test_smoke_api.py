import pytest
from fastapi.testclient import TestClient
# The client fixture will be injected from conftest.py, so we don't need to import app here directly
# from activitylogger.backend.main import app 

# client = TestClient(app) # This is now handled by the 'client' fixture in conftest.py

def test_read_api_docs(client): # Added client fixture
    """Test if the API docs (Swagger UI) are reachable."""
    response = client.get("/docs")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]

def test_read_settings_api(client): # Added client fixture
    """Test if the /api/settings endpoint is reachable and returns JSON."""
    response = client.get("/api/settings")
    assert response.status_code == 200
    assert "application/json" in response.headers["content-type"]
    try:
        data = response.json()
        assert isinstance(data, dict) # Assuming settings are returned as a dictionary
    except ValueError:
        pytest.fail("Response was not valid JSON.")

def test_list_daily_reports_api(client): # Added client fixture
    """Test if the /api/reports/list-reports/daily endpoint is reachable and returns JSON."""
    response = client.get("/api/reports/list-reports/daily")
    assert response.status_code == 200
    assert "application/json" in response.headers["content-type"]
    try:
        data = response.json()
        assert isinstance(data, dict)
        assert "reports" in data
        assert isinstance(data["reports"], list)
    except ValueError:
        pytest.fail("Response was not valid JSON.")

def test_list_weekly_reports_api(client): # Added client fixture
    """Test if the /api/reports/list-reports/weekly endpoint is reachable and returns JSON."""
    response = client.get("/api/reports/list-reports/weekly")
    assert response.status_code == 200
    assert "application/json" in response.headers["content-type"]
    try:
        data = response.json()
        assert isinstance(data, dict)
        assert "reports" in data
        assert isinstance(data["reports"], list)
    except ValueError:
        pytest.fail("Response was not valid JSON.")

def test_list_monthly_reports_api(client): # Added client fixture
    """Test if the /api/reports/list-reports/monthly endpoint is reachable and returns JSON."""
    response = client.get("/api/reports/list-reports/monthly")
    assert response.status_code == 200
    assert "application/json" in response.headers["content-type"]
    try:
        data = response.json()
        assert isinstance(data, dict)
        assert "reports" in data
        assert isinstance(data["reports"], list)
    except ValueError:
        pytest.fail("Response was not valid JSON.")

def test_list_quarterly_reports_api(client): # Added client fixture
    """Test if the /api/reports/list-reports/quarterly endpoint is reachable and returns JSON."""
    response = client.get("/api/reports/list-reports/quarterly")
    assert response.status_code == 200
    assert "application/json" in response.headers["content-type"]
    try:
        data = response.json()
        assert isinstance(data, dict)
        assert "reports" in data
        assert isinstance(data["reports"], list)
    except ValueError:
        pytest.fail("Response was not valid JSON.")

def test_list_annual_reports_api(client): # Added client fixture
    """Test if the /api/reports/list-reports/annual endpoint is reachable and returns JSON."""
    response = client.get("/api/reports/list-reports/annual")
    assert response.status_code == 200
    assert "application/json" in response.headers["content-type"]
    try:
        data = response.json()
        assert isinstance(data, dict)
        assert "reports" in data
        assert isinstance(data["reports"], list)
    except ValueError:
        pytest.fail("Response was not valid JSON.")

def test_update_and_read_settings_api(client): # Added client fixture
    """Test updating settings and reading them back."""
    # 1. Get initial settings (defaults from fresh test DB)
    response_initial = client.get("/api/settings")
    assert response_initial.status_code == 200
    initial_settings = response_initial.json()
    
    # Prepare updated settings data
    updated_notification_interval = initial_settings.get("notificationInterval", 10) + 5
    
    updated_categories = initial_settings.get("categories", [])
    if not updated_categories:
        updated_categories.append({"name": "Work", "groups": ["Coding", "Meetings"]})
    else:
        updated_categories[0]["name"] = updated_categories[0].get("name", "Default") + " Updated"
        if not updated_categories[0].get("groups"):
            updated_categories[0]["groups"] = ["Test Group"]
        else:
            updated_categories[0]["groups"][0] = updated_categories[0]["groups"][0] + " Updated"

    settings_to_update = {
        "notificationInterval": updated_notification_interval,
        "audioDevice": initial_settings.get("audioDevice", "default_device"),
        "llmProvider": initial_settings.get("llmProvider", "default_provider"),
        "openRouterApiKey": initial_settings.get("openRouterApiKey", ""),
        "openRouterLLM": initial_settings.get("openRouterLLM", ""),
        "lmstudioEndpoint": initial_settings.get("lmstudioEndpoint", "http://localhost:1234/v1"),
        "lmstudioModel": initial_settings.get("lmstudioModel", "default_model"),
        "lmstudioLogsModel": initial_settings.get("lmstudioLogsModel"),
        "lmstudioReportsModel": initial_settings.get("lmstudioReportsModel"),
        "categories": updated_categories
    }

    # 2. Update settings
    response_update = client.put("/api/settings", json=settings_to_update)
    assert response_update.status_code == 200
    update_result = response_update.json()
    # Assuming the success message might vary or not be critical for this test, 
    # focusing on data verification. If a specific message is guaranteed, assert it.
    # assert update_result.get("message") == "Settings updated successfully" 

    # 3. Get updated settings and verify changes
    response_updated = client.get("/api/settings")
    assert response_updated.status_code == 200
    final_settings = response_updated.json()

    assert final_settings["notificationInterval"] == updated_notification_interval
    
    # Ensure categories are compared correctly, accounting for potential order changes if not guaranteed
    # For simplicity here, we assume order is preserved or the update logic ensures it.
    assert final_settings["categories"] == updated_categories
    
    assert final_settings["audioDevice"] == initial_settings.get("audioDevice", "default_device")

def test_create_activity(client):
    """Test creating a new activity."""
    activity_data = {
        "timestamp": "2023-01-01T10:00:00Z", # Added 'Z' for UTC to ensure proper parsing
        "description": "Test activity",
        "category": "Work",
        "duration": 60
    }
    response = client.post("/api/activities", json=activity_data)
    assert response.status_code == 200
    data = response.json()
    assert data["description"] == "Test activity"
    assert "id" in data
    return data["id"] # Return ID for subsequent tests

def test_get_activity(client):
    """Test retrieving a specific activity."""
    activity_id = test_create_activity(client) # Create an activity to retrieve
    response = client.get(f"/api/activities/{activity_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == activity_id
    assert data["description"] == "Test activity"

def test_update_activity(client):
    """Test updating an existing activity."""
    activity_id = test_create_activity(client) # Create an activity to update
    updated_data = {
        "timestamp": "2023-01-01T10:00:00Z", # Added 'Z' for UTC
        "description": "Updated test activity",
        "category": "Leisure",
        "duration": 90
    }
    response = client.put(f"/api/activities/{activity_id}", json=updated_data)
    assert response.status_code == 200
    data = response.json()
    assert data["description"] == "Updated test activity"
    assert data["duration"] == 90

    # Verify the update by fetching the activity again
    response_get = client.get(f"/api/activities/{activity_id}")
    assert response_get.status_code == 200
    data_get = response_get.json()
    assert data_get["description"] == "Updated test activity"
    assert data_get["duration"] == 90

def test_delete_activity(client):
    """Test deleting an activity."""
    activity_id = test_create_activity(client) # Create an activity to delete
    response = client.delete(f"/api/activities/{activity_id}")
    assert response.status_code == 200
    assert response.json() == {"message": "Activity deleted successfully"}

    # Verify deletion by trying to retrieve it
    response_get = client.get(f"/api/activities/{activity_id}")
    assert response_get.status_code == 404 # Expecting 404 Not Found after deletion

def test_generate_weekly_report(client):
    """Test triggering weekly report generation."""
    response = client.post("/api/reports/generate-weekly")
    assert response.status_code == 200
    assert "message" in response.json() # Assuming a success message is returned

def test_llm_service_endpoint(client):
    """Test basic reachability of the LLM service endpoint."""
    # This test assumes a /api/llm/process-text endpoint exists and accepts a simple POST
    # Adjust the endpoint and payload based on actual LLM service implementation
    test_payload = {"text": "This is a test for LLM service."}
    response = client.post("/api/llm/process-text", json=test_payload)
    # Expecting 200 OK or 404 Not Found if not implemented/configured
    # If 404, it means the endpoint doesn't exist, which is a valid smoke test outcome
    assert response.status_code in [200, 404]
    if response.status_code == 200:
        assert "response" in response.json() # Assuming some response key

def test_scheduler_status_endpoint(client):
    """Test basic reachability of the scheduler status endpoint."""
    # This test assumes a /api/scheduler/status endpoint exists
    response = client.get("/api/scheduler/status")
    assert response.status_code in [200, 404] # Expecting 200 OK or 404 Not Found
    if response.status_code == 200:
        assert "running" in response.json() # Asserting a known key in the response
        assert response.json()["running"] is False # Asserting the expected value

def test_non_existent_endpoint(client):
    """Test accessing a non-existent endpoint."""
    response = client.get("/api/non-existent-path")
    assert response.status_code == 404

def test_invalid_payload(client):
    """Test sending an invalid payload to an endpoint that expects JSON."""
    invalid_data = "this is not json"
    response = client.post("/api/activities", content=invalid_data, headers={"Content-Type": "application/json"})
    assert response.status_code == 422 # Unprocessable Entity for validation errors

if __name__ == "__main__":
    pytest.main([__file__, "-v"])