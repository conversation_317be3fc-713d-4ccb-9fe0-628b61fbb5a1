# Import base requirements
-r ../requirements-base.txt

# Templates
Jinja2==3.1.5
MarkupSafe==3.0.2

# ML/AI Components
torch==2.6.0
numpy==2.1.3
openai-whisper @ git+https://github.com/openai/whisper.git@517a43ecd132a2089d85f4ebc044728a71d49f6e
tiktoken==0.8.0
numba==0.61.0
llvmlite==0.44.0

# ML Utils
regex==2024.11.6
tqdm==4.67.1
filelock==3.17.0
fsspec==2025.2.0
networkx==3.4.2
sympy==1.13.1
mpmath==1.3.0
more-itertools==10.6.0
# Added npm dependencies for FontAwesome icons:
# @fortawesome/fontawesome-svg-core
# @fortawesome/free-solid-svg-icons
# @fortawesome/react-fontawesome