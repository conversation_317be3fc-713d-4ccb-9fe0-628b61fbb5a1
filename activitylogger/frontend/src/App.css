.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.nav-menu li a {
  color: #333;
  font-size: 1.5em; /* Adjust as needed */
  text-decoration: none;
}

.record-button {
  width: 80px; /* Slightly larger button */
  height: 80px;
  border-radius: 50%;
  border: none;
  transition: background-color 0.3s ease;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1em;
  color: white;
  font-weight: bold;
  text-align: center;
  padding: 0; /* Remove padding to ensure perfect circle */
}

.record-button-idle {
  background-color: green;
}

.record-button-recording {
  background-color: red;
  box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.7);
  animation: pulse-border 1.5s infinite;
}

.record-button-processing {
  background-color: blue;
  color: white; /* Ensure text is visible on blue */
}

@keyframes pulse-border {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 0, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
  }
}
