{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@craco/craco": "^7.1.0", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "axios": "^1.7.9", "cra-template": "1.2.0", "path-browserify": "^1.0.1", "react": "^18.2.0", "react-calendar": "^5.1.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.3", "react-router-dom": "^6.29.0", "react-scripts": "^5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}