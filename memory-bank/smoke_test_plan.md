# Smoke Test Completion Plan

This document outlines the detailed plan to complete the smoke test layer for the ActivityLogger project, based on the assessment of existing tests.

## Current Smoke Test Coverage
The existing smoke tests in `activitylogger/backend/test_smoke_api.py` cover:
- Reachability of API documentation (`/docs`).
- Basic functionality of the settings API (read and update).
- Reachability and basic JSON structure verification for all report listing endpoints (daily, weekly, monthly, quarterly, annual).

The `conftest.py` sets up an isolated in-memory SQLite database for each test function, ensuring a clean and consistent test environment.

## Assessment of Remaining Smoke Test Coverage
To complete the smoke test layer, the following critical areas need to be covered:
1.  **Activity Logging (CRUD Operations)**: The core functionality of logging activities (Create, Read, Update, Delete) is not yet covered.
2.  **Report Generation Trigger**: Triggering the generation of a report is a critical path that should be included.
3.  **LLM Service Integration**: If the LLM service is a core part of the application's functionality, its basic reachability and responsiveness should be smoke tested.
4.  **Scheduler Endpoint**: If there's an API endpoint to interact with or check the scheduler's status, it should be included.
5.  **Basic Error Handling**: Tests for common error scenarios (e.g., invalid endpoints, malformed requests) are important.

## Detailed Plan to Complete the Smoke Test Layer

```mermaid
graph TD
    A[Start Assessment] --> B{Review existing tests: test_smoke_api.py & conftest.py};
    B --> C[Identify Gaps];
    C --> D[Formulate Plan];

    D --> P1[Phase 1: Core API Functionality Smoke Tests];
    P1 --> P1_1[Test POST /api/activities];
    P1 --> P1_2[Test GET /api/activities/{id}];
    P1 --> P1_3[Test PUT /api/activities/{id}];
    P1 --> P1_4[Test DELETE /api/activities/{id}];

    D --> P2[Phase 2: Report Generation Smoke Test];
    P2 --> P2_1[Test POST /api/reports/generate-weekly];

    D --> P3[Phase 3: LLM Service & Scheduler Smoke Tests];
    P3 --> P3_1[Test LLM Service Endpoint];
    P3 --> P3_2[Test Scheduler Endpoint];

    D --> P4[Phase 4: Basic Error Handling Smoke Tests];
    P4 --> P4_1[Test Invalid Endpoint (404)];
    P4 --> P4_2[Test Invalid Payload (400/422)];

    P1_4 --> E[Review and Refine Plan];
    P2_1 --> E;
    P3_2 --> E;
    P4_2 --> E;
    E --> F[Present Plan to User];
    F --> G{User Approval?};
    G --> |Yes| H[Switch to Code Mode for Implementation];
    G --> |No| D;
```

### Phase 1: Core API Functionality Smoke Tests
- **Objective**: Verify the basic Create, Read, Update, and Delete (CRUD) operations for activities.
- **Actions**:
    - Add a test for `POST /api/activities` to create a new activity. Assert a successful response (e.g., 200 OK or 201 Created) and the structure of the returned activity data.
    - Add a test for `GET /api/activities/{activity_id}`. This test will use the ID of the activity created in the previous step to retrieve it and verify its content.
    - Add a test for `PUT /api/activities/{activity_id}` to update an existing activity. Verify the update by fetching the activity again.
    - Add a test for `DELETE /api/activities/{activity_id}` to remove an activity. Verify deletion by attempting to retrieve it and expecting a 404 Not Found.

### Phase 2: Report Generation Smoke Test
- **Objective**: Ensure that the report generation process can be successfully triggered.
- **Actions**:
    - Add a test for `POST /api/reports/generate-weekly` (or the relevant endpoint for triggering report generation). Assert a successful response (e.g., 202 Accepted for asynchronous tasks).

### Phase 3: LLM Service and Scheduler Smoke Tests (Conditional)
- **Objective**: Verify the basic reachability of the LLM service and scheduler if they expose API endpoints.
- **Actions**:
    - If an LLM service endpoint exists (e.g., `/api/llm/process-text`), add a basic `POST` test to ensure it responds.
    - If a scheduler status or trigger endpoint exists (e.g., `/api/scheduler/status`), add a `GET` test to check its reachability.

### Phase 4: Basic Error Handling Smoke Tests
- **Objective**: Confirm that the API provides appropriate responses for common invalid requests.
- **Actions**:
    - Add a test to `GET` a non-existent API endpoint and assert a 404 Not Found status code.
    - Add a test to `POST` or `PUT` an invalid JSON payload to a critical endpoint (e.g., `/api/activities`) and assert an appropriate error status code (e.g., 400 Bad Request or 422 Unprocessable Entity).